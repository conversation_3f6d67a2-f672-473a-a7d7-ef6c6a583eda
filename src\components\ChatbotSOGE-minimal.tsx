import React, { useState, useEffect, useRef } from 'react';
import { Send, Settings, History, X, Bot, User, Server, Webhook, Clock, MessageSquare, ChevronDown } from 'lucide-react';
import { useChatbotUnificado } from '../hooks/useChatbotUnificado';
import type { Mensagem } from '../hooks/useChatbotAPI';
import type { ConfiguracaoUnificada } from '../hooks/useChatbotUnificado';
import { sessionManager, type SessionData, type StoredMessage } from '../lib/SessionManager';
import ConfiguracoesChatbot from './ConfiguracoesChatbot';
import HistoricoSessoes from './HistoricoSessoes';
import { Button } from '@/src/components/ui/button';
import { Textarea } from '@/src/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Avatar, AvatarFallback } from '@/src/components/ui/avatar';
import { Badge } from '@/src/components/ui/badge';
import { SimpleMarkdown } from '@/src/components/ui/simple-markdown';
import { DEFAULT_STORAGE_CONFIG } from '../lib/SessionStorage';
import styles from './ChatbotSOGE-minimal.module.css';

const ChatbotSOGE: React.FC = () => {
  const [mensagens, setMensagens] = useState<Mensagem[]>([]);
  const [inputMensagem, setInputMensagem] = useState('');
  const [sessaoId, setSessaoId] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [mostrarConfiguracoes, setMostrarConfiguracoes] = useState(false);
  const [mostrarHistorico, setMostrarHistorico] = useState(false);
  const [mostrarScrollButton, setMostrarScrollButton] = useState(false);
  const [configuracao, setConfiguracao] = useState<ConfiguracaoUnificada>({
    tipoConexao: 'webhook',
    apiUrl: 'http://localhost:8000',
    webhookUrl: 'https://n8n.sondtheanime.site/webhook/chat',
    timeout: 30000,
    maxTentativas: 3,
    habilitarLogs: false,
    usuarioId: `user_${Date.now()}`,
    sessionManagement: {
      enabled: true,
      maxStoredSessions: DEFAULT_STORAGE_CONFIG.maxStoredSessions,
      autoCleanupDays: DEFAULT_STORAGE_CONFIG.autoCleanupDays,
      enableDetailedLogging: DEFAULT_STORAGE_CONFIG.enableDetailedLogging,
      compressionEnabled: DEFAULT_STORAGE_CONFIG.compressionEnabled
    }
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { enviarMensagem: enviarMensagemAPI, finalizarSessao: finalizarSessaoAPI, carregando } = useChatbotUnificado(configuracao);

  // Enhanced session management functions using SessionManager
  const carregarSessaoAtiva = (): { sessaoId: string | null; mensagens: Mensagem[]; sessionData: SessionData | null } => {
    try {
      // Try to get current session from SessionManager
      const currentSessionId = sessionManager.getCurrentSessionId();
      
      if (currentSessionId) {
        const sessionData = sessionManager.loadSession(currentSessionId);
        
        if (sessionData) {
          // Convert StoredMessage[] to Mensagem[] for UI compatibility
          const mensagens: Mensagem[] = sessionData.messages.map((msg: StoredMessage) => ({
            id: msg.id,
            tipo: msg.type === 'user' ? 'user' : 'assistant',
            conteudo: msg.content,
            timestamp: msg.timestamp
          }));

          return { 
            sessaoId: currentSessionId, 
            mensagens, 
            sessionData 
          };
        }
      }

      // Fallback: try to load from old localStorage format for backward compatibility
      const sessaoId = localStorage.getItem('chatbot-sessao-ativa');
      const mensagensSalvas = localStorage.getItem('chatbot-mensagens-sessao');

      if (sessaoId && mensagensSalvas) {
        const mensagens = JSON.parse(mensagensSalvas);
        return { sessaoId, mensagens, sessionData: null };
      }
    } catch (error) {
      console.error('Erro ao carregar sessão ativa:', error);
    }

    return { sessaoId: null, mensagens: [], sessionData: null };
  };

  const salvarSessaoAtiva = (sessaoId: string, mensagens: Mensagem[]) => {
    try {
      // For webhook connections, session is managed by SessionManager automatically
      if (configuracao.tipoConexao === 'webhook') {
        // SessionManager handles persistence automatically
        return;
      }

      // For API connections, maintain backward compatibility
      localStorage.setItem('chatbot-sessao-ativa', sessaoId);
      localStorage.setItem('chatbot-mensagens-sessao', JSON.stringify(mensagens));
    } catch (error) {
      console.error('Erro ao salvar sessão ativa:', error);
    }
  };

  const limparSessaoAtiva = () => {
    try {
      if (sessaoId) {
        // Finalize session in SessionManager for webhook connections
        if (configuracao.tipoConexao === 'webhook') {
          sessionManager.finalizeSession(sessaoId);
        }
      }

      // Clear old localStorage keys for backward compatibility
      localStorage.removeItem('chatbot-sessao-ativa');
      localStorage.removeItem('chatbot-mensagens-sessao');
    } catch (error) {
      console.error('Erro ao limpar sessão ativa:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
      setMostrarScrollButton(!isNearBottom && mensagens.length > 3);
    }
  };

  const scrollToBottomInstant = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [mensagens]);

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [mensagens.length]);

  useEffect(() => {
    // Carregar configurações salvas
    try {
      const configSalva = localStorage.getItem('chatbot-config');
      if (configSalva) {
        const config = JSON.parse(configSalva);
        setConfiguracao(config);
      }
    } catch (error) {
      console.error('Erro ao carregar configuração:', error);
    }

    // Tentar recuperar sessão ativa salva usando SessionManager
    const { sessaoId: sessaoSalva, mensagens: mensagensSalvas, sessionData: sessionDataSalva } = carregarSessaoAtiva();

    if (sessaoSalva && mensagensSalvas.length > 0) {
      // Restaurar sessão salva
      setSessaoId(sessaoSalva);
      setMensagens(mensagensSalvas);
      setSessionData(sessionDataSalva);
    } else {
      // Mensagem de boas-vindas para nova sessão
      const mensagemBoasVindas: Mensagem = {
        id: 'welcome',
        tipo: 'assistant',
        conteudo: `Olá! Sou o **assistente SOGE** da FibraLink.

Como posso apoiá-lo hoje na resolução de casos integração dos aplicativos?

Digite sua dúvida ou cole logs para análise!`,
        timestamp: new Date().toISOString()
      };
      setMensagens([mensagemBoasVindas]);
    }
  }, []);

  const enviarMensagem = async () => {
    if (!inputMensagem.trim() || carregando) return;

    const novaMensagem: Mensagem = {
      id: `user_${Date.now()}`,
      tipo: 'user',
      conteudo: inputMensagem,
      timestamp: new Date().toISOString()
    };

    setMensagens(prev => [...prev, novaMensagem]);
    const mensagemEnviada = inputMensagem;
    setInputMensagem('');

    try {
      const resposta = await enviarMensagemAPI(
        mensagemEnviada,
        sessaoId,
        configuracao.usuarioId,
        {
          fonte: 'plasmo_extension',
          user_agent: navigator.userAgent
        }
      );

      // Atualizar sessão se for a primeira mensagem
      const novoSessaoId = sessaoId || resposta.sessao_id;
      if (!sessaoId) {
        setSessaoId(resposta.sessao_id);
      }

      // Adicionar resposta do assistente
      const respostaAssistente: Mensagem = {
        id: resposta.mensagem_assistente.id,
        tipo: 'assistant',
        conteudo: resposta.mensagem_assistente.conteudo,
        timestamp: resposta.mensagem_assistente.timestamp
      };

      const novasMensagens = [...mensagens, novaMensagem, respostaAssistente];
      setMensagens(novasMensagens);

      // Update session data for webhook connections
      if (configuracao.tipoConexao === 'webhook' && novoSessaoId) {
        try {
          // Load updated session data from SessionManager
          const updatedSessionData = sessionManager.loadSession(novoSessaoId);
          if (updatedSessionData) {
            setSessionData(updatedSessionData);
          }
        } catch (error) {
          console.error('Erro ao atualizar dados da sessão:', error);
        }
      }

      // Salvar sessão ativa no localStorage (for API connections)
      salvarSessaoAtiva(novoSessaoId, novasMensagens);

    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      const mensagemErro: Mensagem = {
        id: `error_${Date.now()}`,
        tipo: 'assistant',
        conteudo: 'Desculpe, ocorreu um erro ao conectar com o servidor. Verifique se o backend está rodando e as configurações estão corretas.',
        timestamp: new Date().toISOString()
      };
      setMensagens(prev => [...prev, mensagemErro]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      enviarMensagem();
    }
  };

  const finalizarSessao = async () => {
    if (!sessaoId) return;

    try {
      await finalizarSessaoAPI(sessaoId);

      // Limpar dados da sessão do localStorage
      limparSessaoAtiva();

      setSessaoId(null);
      setSessionData(null);
      setMensagens([{
        id: 'session_ended',
        tipo: 'assistant',
        conteudo: 'Sessão finalizada. Obrigado por usar o assistente SOGE!',
        timestamp: new Date().toISOString()
      }]);
    } catch (error) {
      console.error('Erro ao finalizar sessão:', error);
    }
  };

  const salvarConfiguracoes = (novaConfig: ConfiguracaoUnificada) => {
    console.log('Salvando configuração:', novaConfig);
    setConfiguracao(novaConfig);
    setMostrarConfiguracoes(false);

    // Atualizar configurações do SessionManager se o gerenciamento de sessão estiver habilitado
    if (novaConfig.sessionManagement.enabled) {
      sessionManager.updateStorageConfig({
        maxStoredSessions: novaConfig.sessionManagement.maxStoredSessions,
        autoCleanupDays: novaConfig.sessionManagement.autoCleanupDays,
        enableDetailedLogging: novaConfig.sessionManagement.enableDetailedLogging,
        compressionEnabled: novaConfig.sessionManagement.compressionEnabled
      });
    }

    // Salvar no localStorage
    localStorage.setItem('chatbot-config', JSON.stringify(novaConfig));
  };

  const carregarSessaoDoHistorico = (sessaoId: string, mensagens: Mensagem[]) => {
    setSessaoId(sessaoId);
    setMensagens(mensagens);
    setMostrarHistorico(false);

    // For webhook connections, load session data from SessionManager
    if (configuracao.tipoConexao === 'webhook') {
      try {
        const sessionData = sessionManager.loadSession(sessaoId);
        if (sessionData) {
          setSessionData(sessionData);
          
          // Convert StoredMessage[] to Mensagem[] for consistency
          const convertedMessages: Mensagem[] = sessionData.messages.map((msg: StoredMessage) => ({
            id: msg.id,
            tipo: msg.type === 'user' ? 'user' : 'assistant',
            conteudo: msg.content,
            timestamp: msg.timestamp
          }));
          
          setMensagens(convertedMessages);
        }
      } catch (error) {
        console.error('Erro ao carregar dados da sessão do histórico:', error);
      }
    }

    // Salvar nova sessão carregada (for API connections)
    salvarSessaoAtiva(sessaoId, mensagens);
  };

  // Carregar configurações do localStorage na inicialização
  useEffect(() => {
    const configSalva = localStorage.getItem('chatbot-config');
    if (configSalva) {
      try {
        const config = JSON.parse(configSalva);
        // Garantir que a configuração tenha todos os campos necessários
        const configCompleta: ConfiguracaoUnificada = {
          tipoConexao: config.tipoConexao || 'webhook',
          apiUrl: config.apiUrl || 'http://localhost:8000',
          webhookUrl: config.webhookUrl || 'https://n8n.sondtheanime.site/webhook/chat',
          timeout: config.timeout || 30000,
          maxTentativas: config.maxTentativas || 3,
          habilitarLogs: config.habilitarLogs || false,
          usuarioId: config.usuarioId || `user_${Date.now()}`,
          sessionManagement: config.sessionManagement || {
            enabled: true,
            maxStoredSessions: DEFAULT_STORAGE_CONFIG.maxStoredSessions,
            autoCleanupDays: DEFAULT_STORAGE_CONFIG.autoCleanupDays,
            enableDetailedLogging: DEFAULT_STORAGE_CONFIG.enableDetailedLogging,
            compressionEnabled: DEFAULT_STORAGE_CONFIG.compressionEnabled
          }
        };
        setConfiguracao(configCompleta);

        // Aplicar configurações de sessão ao SessionManager
        if (configCompleta.sessionManagement.enabled) {
          sessionManager.updateStorageConfig({
            maxStoredSessions: configCompleta.sessionManagement.maxStoredSessions,
            autoCleanupDays: configCompleta.sessionManagement.autoCleanupDays,
            enableDetailedLogging: configCompleta.sessionManagement.enableDetailedLogging,
            compressionEnabled: configCompleta.sessionManagement.compressionEnabled
          });
        }
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
      }
    }
  }, []);

  return (
    <>
      <Card className={`w-full h-full flex flex-col shadow-none border-0 bg-transparent ${styles.chatContainer}`}>
        <CardHeader className="pb-3 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                Assistente SOGE
                {configuracao.tipoConexao === 'api' ? (
                  <Server className="h-4 w-4 text-blue-500" />
                ) : (
                  <Webhook className="h-4 w-4 text-green-500" />
                )}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                FibraLink • {configuracao.tipoConexao === 'api' ? 'API Local' : 'Webhook N8N'}
              </p>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMostrarHistorico(true)}
                className="h-8 w-8"
              >
                <History className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMostrarConfiguracoes(true)}
                className="h-8 w-8"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          {sessaoId && (
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {sessaoId.slice(0, 8)}...
                </Badge>
                {configuracao.tipoConexao === 'webhook' && sessionData && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <MessageSquare className="h-3 w-3" />
                    <span>{sessionData.metadata.messageCount}</span>
                    <Clock className="h-3 w-3 ml-1" />
                    <span>
                      {new Date(sessionData.metadata.lastActivity).toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={finalizarSessao}
                className="h-6 w-6"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0 min-h-0">
          {/* Área de mensagens com scroll melhorado */}
          <div className="flex-1 overflow-hidden relative">
            <div
              ref={messagesContainerRef}
              className={`h-full overflow-y-auto overflow-x-hidden p-4 space-y-4 scroll-smooth ${styles.chatMessages}`}
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgb(203 213 225) transparent'
              }}
            >
              {mensagens.map((msg, index) => (
                <div
                  key={msg.id}
                  className={`flex gap-3 ${msg.tipo === 'user' ? 'flex-row-reverse' : 'flex-row'} ${styles.messageAnimation}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className={msg.tipo === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}>
                      {msg.tipo === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`max-w-[75%] rounded-lg px-3 py-2 text-sm ${
                    msg.tipo === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  } ${styles.messageContent}`}>
                    {msg.tipo === 'user' ? (
                      <p className="whitespace-pre-wrap break-words">{msg.conteudo}</p>
                    ) : (
                      <SimpleMarkdown className="text-sm prose prose-sm max-w-none">
                        {msg.conteudo}
                      </SimpleMarkdown>
                    )}
                    <p className="text-xs opacity-70 mt-1">
                      {new Date(msg.timestamp).toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              ))}
              {carregando && (
                <div className={`flex gap-3 ${styles.messageAnimation}`}>
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-muted">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-muted rounded-lg px-3 py-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.4s'}}></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Gradiente sutil para indicar scroll */}
            <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-background/80 to-transparent pointer-events-none" />
            <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background/80 to-transparent pointer-events-none" />

            {/* Botão para rolar para baixo */}
            {mostrarScrollButton && (
              <Button
                variant="secondary"
                size="icon"
                className={`absolute bottom-4 right-4 h-8 w-8 rounded-full shadow-lg transition-all duration-300 ${styles.scrollIndicator} ${mostrarScrollButton ? 'visible' : ''}`}
                onClick={scrollToBottomInstant}
              >
                <ChevronDown className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Área de input fixada na parte inferior */}
          <div className="p-4 border-t bg-background/95 backdrop-blur flex-shrink-0">
            <div className="flex gap-2">
              <Textarea
                value={inputMensagem}
                onChange={(e) => setInputMensagem(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Digite sua mensagem sobre streaming..."
                disabled={carregando}
                rows={2}
                className={`resize-none min-h-[60px] max-h-[120px] ${styles.messageInput}`}
              />
              <Button
                onClick={enviarMensagem}
                disabled={carregando || !inputMensagem.trim()}
                size="icon"
                className="h-auto self-end"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modais */}
      {mostrarConfiguracoes && (
        <ConfiguracoesChatbot
          onClose={() => setMostrarConfiguracoes(false)}
          onSave={salvarConfiguracoes}
          configuracaoAtual={configuracao}
        />
      )}

      {mostrarHistorico && (
        <HistoricoSessoes
          onClose={() => setMostrarHistorico(false)}
          onCarregarSessao={carregarSessaoDoHistorico}
          configuracao={configuracao}
        />
      )}
    </>
  );
};

export default ChatbotSOGE;
